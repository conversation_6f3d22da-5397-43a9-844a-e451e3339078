{
  "$schema": "https://smapi.io/schemas/content-patcher.json",
  "Format": "2.0.0",

  "ConfigSchema": {

    "Lantana_Disable": {
      "AllowValues": "true, false",
      "Default": "false"
    },
    "PG": {
      "AllowValues": "true, false",
      "Default": "false"
    },
    "Lantana_Wedding": {
      "AllowValues": "true, false",
      "Default": "true"
    }
  },




  "DynamicTokens": [
    {
      "Name": "LantanaWeddingLocation",
      "Value": "MermaidLantana 22 71 0"
    },
    {
      "Name": "LantanaWeddingLocation",
      "Value": "MermaidLantana 500 500 0",
      "When": {
        "Lantana_Wedding": false
      }
    },
    {
      "Name": "LantanaSprite",
      "Value": "assets/Lantana/spritesheet.png"
    },
    {
      "Name": "LantanaSprite",
      "Value": "assets/Lantana/spritesheetPG.png",
      "When": {
        "PG": true
      }
    }




    //"assets/Lantana/spritesheet.png"
  ],



  "Changes": [


    {
      "Action": "Load",
      "FromFile": "Assets/AineFlower.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/AineFlower"
    },

    {
      "Action": "Load",
      "FromFile": "Assets/AineFlowerSeeds.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/AineFlowerSeeds"
    },

    {
      "Action": "Load",
      "FromFile": "Assets/AineFlowerCrop.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/AineFlowerCrop"
    },


    {
      "Action": "Load",
      "FromFile": "Assets/AphroditeFlower.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/AphroditeFlower"
    },

    {
      "Action": "Load",
      "FromFile": "Assets/AphroditeFlowerSeeds.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/AphroditeFlowerSeeds"
    },

    {
      "Action": "Load",
      "FromFile": "Assets/AphroditeFlowerCrop.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/AphroditeFlowerCrop"
    },

    {
      "Action": "Load",
      "FromFile": "Assets/LilithToken.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/LilithToken"
    },

    {
      "Action": "Load",
      "FromFile": "Assets/AtagartisPendant.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/AtagartisPendant"
    },

 {
      "Action": "Load",
      "FromFile": "Assets/CeresPendant.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/CeresPendant"
    },

    {
      "Action": "Load",
      "FromFile": "Assets/MermaidBouquet.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/MermaidBouquet"
    },

    {
      "Action": "Load",
      "FromFile": "Assets/RoomieBGone.png",
      "Target": "Mods/ApryllForever.CPPolyamorySweet/RoomieBGone"
    },




    {
      "Action": "EditData",
      "Target": "Data/Crops",
      "Entries": {
        "ApryllForever.CPPolyamorySweet_AineFlowerSeeds": {
          "Seasons": [ "spring", "summer", "fall" ],
          "DaysInPhase": [ 1, 2, 3, 3 ], // grows in 9 days with four growing sprites
          "HarvestItemId": "ApryllForever.CPPolyamorySweet_AineFlower",
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/AineFlowerCrop",
          "SpriteIndex": 0
        },
        "ApryllForever.CPPolyamorySweet_AphroditeFlowerSeeds": {
          "Seasons": [ "spring", "summer", "fall", "winter" ],
          "DaysInPhase": [ 1, 1, 2, 2 ], // grows in 6 days with four growing sprites
          "HarvestItemId": "ApryllForever.CPPolyamorySweet_AphroditeFlower",
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/AphroditeFlowerCrop",
          "SpriteIndex": 0
        }
      }
    },
    {
      "Action": "EditData",
      "Target": "Data/Objects",
      "Entries": {
        "ApryllForever.CPPolyamorySweet_AineFlower": {
          "Name": "Áine Flower",
          "DisplayName": "安雅花",
          "Description": "安雅花，以爱尔兰夏季、爱情与性欲的女神Áine命名（发音为Anya或者Awnya），它最初在爱尔兰的安雅湖畔盛开。将安雅花赠予伴侣，以宣告TA是你的正宫。或者，把它作为礼物送给你爱的人！",
          "Type": "Basic",
          "Category": -80,
          "Price": 313,
          "Edibility": -300,
          "IsDrink": false,
          "ExcludeFromShippingCollection": true,
          "ExcludeFromRandomSale": true,
          "Buff": {


          },
          "ContextTags": [ "color_green", "fairy_item" ],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/AineFlower",
          "SpriteIndex": 0
        }

      }
    },


    {
      "Action": "EditData",
      "Target": "Data/Objects",
      "Entries": {
        "ApryllForever.CPPolyamorySweet_AphroditeFlower": {
          "Name": "Aphrodite Flower",
          "DisplayName": "阿佛洛狄忒之花",
          "Description": "以古代爱情、性欲和战争的女神阿佛洛狄忒Aphrodite命名。将阿佛洛狄忒之花送给你想要一起孕育生命的爱人！",
          "Type": "Basic",
          "Category": -80,
          "Price": 137,
          "Edibility": -300,
          "IsDrink": false,
          "ExcludeFromShippingCollection": true,
          "ExcludeFromRandomSale": true,
          "Buff": {


          },
          "ContextTags": [ "color_green", "fairy_item", ],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/AphroditeFlower",
          "SpriteIndex": 0
        }

      }
    },


    {
      "Action": "EditData",
      "Target": "Data/Objects",
      "Entries": {
        "ApryllForever.CPPolyamorySweet_LilithToken": {
          "Name": "Lilith Token",
          "DisplayName": "莉莉丝的信物",
          "Description": "莉莉丝Lilith，弃儿之母，会欣然带走你通过信物献上的孩子。但此事永远无法挽回。",
          "Type": "Basic",
          "Category": -80,
          "Price": 137,
          "Edibility": -300,
          "IsDrink": false,
          "ExcludeFromShippingCollection": true,
          "ExcludeFromRandomSale": true,
          "Buff": {


          },
          "ContextTags": [ "color_grey", "fairy_item" ],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/LilithToken",
          "SpriteIndex": 0
        }

      }
    },

    {

      "Action": "EditData",
      "Target": "Data/Objects",
      "Entries": {

        "ApryllForever.CPPolyamorySweet_AineFlowerSeeds": {
          "Name": "Áine Flower Seeds",
          "DisplayName": "安雅花种子",
          "Description": "安雅花从春天到秋天都会生长，恰巧，它们传统的开花日期与花舞节吻合，需要9天才能成熟。",
          "Type": "Seeds",
          "Category": -74,
          "Price": 40,
          "Edibility": -300,
          "IsDrink": false,
          "Buff": {
          },
          "ContextTags": [],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/AineFlowerSeeds",
          "ExcludeFromShippingCollection": true,
          "SpriteIndex": 0
        },

        "ApryllForever.CPPolyamorySweet_AphroditeFlowerSeeds": {
          "Name": "Aphrodite Flower Seeds",
          "DisplayName": "阿佛洛狄忒之花种子",
          "Description": "阿佛洛狄忒之花，就像爱一样，在任何季节都能生长！成熟需要6天。",
          "Type": "Seeds",
          "Category": -74,
          "Price": 30,
          "Edibility": -300,
          "IsDrink": false,
          "Buff": {
          },
          "ContextTags": [],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/AphroditeFlowerSeeds",
          "ExcludeFromShippingCollection": true,
          "SpriteIndex": 0
        },

        "ApryllForever.CPPolyamorySweet_AtagartisPendant": {
          "Name": "Atagartis Pendant",
          "DisplayName": "阿塔加蒂斯吊坠",
          "Description": "以美人鱼女神阿塔加蒂斯Atagartis为名。将这个吊坠送给你想结婚的人！",
          "Type": "Basic",
          "Category": -80,
          "Price": 400,
          "Edibility": -300,
          "IsDrink": false,
          "Buff": {


          },
          "ContextTags": [ "color_bisexual", "mermaid_item" ],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/AtagartisPendant",
          "ExcludeFromShippingCollection": true,
          "SpriteIndex": 0
        },
         "ApryllForever.CPPolyamorySweet_CeresPendant": {
          "Name": "Ceres Pendant",
          "DisplayName": "农夫吊坠",
          "Description": "在山谷里，当农夫想和另一名农夫结婚时，会给出一枚特殊的美人鱼吊坠，吊坠有着防风草般的颜色！",
          "Type": "Basic",
          "Category": -80,
          "Price": 400,
          "Edibility": -300,
          "IsDrink": false,
          "Buff": {


          },
          "ContextTags": [ "color_green", "mermaid_item" ],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/CeresPendant",
          "ExcludeFromShippingCollection": true,
          "SpriteIndex": 0
        },
        "ApryllForever.CPPolyamorySweet_MermaidBouquet": {
          "Name": "Mermaid Bouquet",
          "DisplayName": "美人鱼花束",
          "Description": "一束由紫色、粉色和蓝色花朵组成的美丽花束！把这束花送给你想约会的对象！",
          "Type": "Basic",
          "Category": -80,
          "Price": 400,
          "Edibility": -300,
          "IsDrink": false,
          "Buff": {
          },
          "ContextTags": [ "color_bisexual", "mermaid_item" ],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/MermaidBouquet",
          "ExcludeFromShippingCollection": true,
          "SpriteIndex": 0
        }
      }

    },

    {
      "Action": "EditData",
      "Target": "Data/Objects",
      "Entries": {
        "ApryllForever.CPPolyamorySweet_RoomieBGone": {
          "Name": "Roomie B Gone",
          "DisplayName": "分居蜡烛",
          "Description": "对某一个室友感到厌倦了吗？这根神奇的蜡烛会让TA永远离开！将蜡烛送给TA，让蜡烛蕴含的强大黑暗能量替你说‘再见’！",
          "Type": "Basic",
          "Category": -80,
          "Price": 40,
          "Edibility": -300,
          "IsDrink": false,
          "Buff": {


          },
          "ContextTags": [ "color_green", "fairy_item" ],
          "Miscellaneous": null,
          "Texture": "Mods/ApryllForever.CPPolyamorySweet/RoomieBGone",
          "ExcludeFromShippingCollection": true,
          "SpriteIndex": 0
        }
      }
    },

    {
      "Action": "EditData",
      "Target": "Data/Shops",
      "TargetField": [ "SeedShop", "Items" ],
      "Entries": {
        "ApryllForever.CPPolyamorySweet_AineFlower": {
          "Id": "ApryllForever.CPPolyamorySweet_AineFlower",
          "ItemId": "ApryllForever.CPPolyamorySweet_AineFlower",
          "Price": 400
        }
      }
    },

    {
      "Action": "EditData",
      "Target": "Data/Shops",
      "TargetField": [ "SeedShop", "Items" ],
      "Entries": {
        "ApryllForever.CPPolyamorySweet_AineFlowerSeeds": {
          "Id": "ApryllForever.CPPolyamorySweet_AineFlowerSeeds",
          "ItemId": "ApryllForever.CPPolyamorySweet_AineFlowerSeeds",
          "Price": 80
        }
      }
    },

    {
      "Action": "EditData",
      "Target": "Data/Shops",
      "TargetField": [ "SeedShop", "Items" ],
      "Entries": {
        "ApryllForever.CPPolyamorySweet_AphroditeFlower": {
          "Id": "ApryllForever.CPPolyamorySweet_AphroditeFlower",
          "ItemId": "ApryllForever.CPPolyamorySweet_AphroditeFlower",
          "Price": 400
        }
      }
    },

    {
      "Action": "EditData",
      "Target": "Data/Shops",
      "TargetField": [ "SeedShop", "Items" ],
      "Entries": {
        "ApryllForever.CPPolyamorySweet_AphroditeFlowerSeeds": {
          "Id": "ApryllForever.CPPolyamorySweet_AphroditeFlowerSeeds",
          "ItemId": "ApryllForever.CPPolyamorySweet_AphroditeFlowerSeeds",
          "Price": 80
        }
      }
    },


    {
      "Action": "EditData",
      "Target": "Strings/StringsFromCSFiles",
      "Entries": {
        "AineFlower_reject": "我...真的觉得我们的关系还没到这个地步，我不能收下这个...",
        "AineFlower_accept": "哇哦 谢谢你！！！你对我来说也是特别的人！",
        "AineFlower_spouse": "哇哦 谢谢你！！！我也很爱你！我愿意为你做任何事！$h",
        "AphroditeFlower_accept": "...！！！$6#$b#当然！我很想和你一起要个孩子！$1",
        "AphroditeFlower_reject": "什么？！？！？！？你肯定是疯了！",
        "AphroditeFlower_notVillager": "这不是适合这个人的礼物。",
        "AphroditeFlower_alreadyGiven": "你今天已经给了{0}一朵阿佛洛狄忒之花。",
        "LilithToken_accept": "这太棒了！我爱这黑暗能量！莉莉丝的爱让我感受到被爱包围！$1",
        "LilithToken_reject": "啊！！！！$5#$b#你讨厌我吗？$2",

        "NPC.cs.4434": "唔...这是什么？我快睡着了。$a",
        "NPC.cs.4435": "我今天不太想起床。$s",
        "NPC.cs.4436": "*叹气*...我只想待在床上。$s",
        "NPC.cs.4437": "唔...这是什么？去做早饭吧。$a",
        "NPC.cs.4438": "...$a#$e#我做了个噩梦，没什么大事。$s",
        "RoomieBGone": "什么？！？！？$2#$b#我会走的...",
        "NotRoomie": "我已经...不是你的室友了。#$b#笨蛋！$u",

        "PolyamorySweet.GaveBirthToday": "我们的新宝宝要加入我们的生活了，我太高兴了！$1",
        "WifeGreeting": "嘿，亲爱的！很高兴看到你出来走走！#$b#见到你会让我的一天变得更好！$6"

        

      }
    },

    // Minecarts, Jaqi!!!


    {
      "Action": "EditData",
      "Target": "Data/Minecarts",
      "TargetField": [ "Default", "Destinations" ],
      "Entries": {
        "Lantana": {

          "Id": "Lantana",
          "DisplayName": "兰塔娜礁",
          "TargetLocation": "Custom_LantanaLagoon",
             "When": {

       "Lantana_Disable": false
      },
          "TargetTile": {
            "X": 15,
            "Y": 15
          },
          "TargetDirection": "down"
        }
      }
      // "When": {
      //   "HasFlag": "ccBoilerRoom, jojaBoilerRoom",
      //"query: {{DaysPlayed}} >= 32": true,
      //  "Lantana_Disable": false
      //}
    },


    {
      "Action": "EditData",
      "Target": "Data/Shops",
      "When": {
        "Lantana_Disable": false
      },
      "Entries": {
        "PolyamorySweet.LantanaLoveShop": {
          "Currency": 0,
          "StackSizeVisibility": null,
          "OpenSound": "pullItemFromWater",
          "PurchaseSound": "crystal",
          "PurchaseRepeatSound": "crystal",
          "PriceModifiers": null,
          "PriceModifierMode": "Stack",
          "Owners": [
            {
              "Condition": null,
              "Portrait": "MermaidLantana",
              "Dialogues": [
                {
                  "Id": "Default",
                  "Condition": null,
                  "Dialogue": null,
                  "RandomDialogue": [
                    "嗨亲爱的！请尽情挑选我精心准备的商品？"
                  ]
                }
              ],
              "RandomizeDialogueOnOpen": true,
              "Id": "MermaidLantana",
              "Name": "MermaidLantana"
            }
          ],
          "VisualTheme": null,
          "SalableItemTags": [
            "category_building_resources",
            "category_sell_at_fish_shop",
            "category_sell_at_pierres"
          ],
          "Items": [

            {
              "TradeItemId": null,
              "TradeItemAmount": 1,
              "Price": 80,
              "ApplyDifficultyModifier": null,
              "AvailableStock": -1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": null,
              "Id": "ApryllForever.CPPolyamorySweet_AineFlowerSeeds",
              "ItemId": "ApryllForever.CPPolyamorySweet_AineFlowerSeeds",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "TradeItemId": null,
              "TradeItemAmount": 1,
              "Price": 70,
              "ApplyDifficultyModifier": null,
              "AvailableStock": -1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": null,
              "Id": "ApryllForever.CPPolyamorySweet_AphroditeFlowerSeeds",
              "ItemId": "ApryllForever.CPPolyamorySweet_AphroditeFlowerSeeds",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "TradeItemId": null,
              "TradeItemAmount": 1,
              "Price": 400,
              "ApplyDifficultyModifier": null,
              "AvailableStock": -1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": null,
              "Id": "ApryllForever.CPPolyamorySweet_AineFlower",
              "ItemId": "ApryllForever.CPPolyamorySweet_AineFlower",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "TradeItemId": null,
              "TradeItemAmount": 1,
              "Price": 300,
              "ApplyDifficultyModifier": null,
              "AvailableStock": -1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": null,
              "Id": "ApryllForever.CPPolyamorySweet_AphroditeFlower",
              "ItemId": "ApryllForever.CPPolyamorySweet_AphroditeFlower",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "TradeItemId": 74,
              "TradeItemAmount": 1,
              "Price": 50000,
              "ApplyDifficultyModifier": null,
              "AvailableStock": -1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": null,
              "Id": "ApryllForever.CPPolyamorySweet_LilithToken",
              "ItemId": "ApryllForever.CPPolyamorySweet_LilithToken",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "TradeItemId": null,
              "TradeItemAmount": 1,
              "Price": 200,
              "ApplyDifficultyModifier": null,
              "AvailableStock": -1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": null,
              "Id": "ApryllForever.CPPolyamorySweet_MermaidBouquet",
              "ItemId": "ApryllForever.CPPolyamorySweet_MermaidBouquet",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "TradeItemId": null,
              "TradeItemAmount": 1,
              "Price": 5000,
              "ApplyDifficultyModifier": null,
              "AvailableStock": 1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": "WEATHER here Rain Snow Storm",
              "Id": "ApryllForever.CPPolyamorySweet_AtagartisPendant",
              "ItemId": "ApryllForever.CPPolyamorySweet_AtagartisPendant",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },

            {
              "TradeItemId": null,
              "TradeItemAmount": 1,
              "Price": 16061,
              "ApplyDifficultyModifier": null,
              "AvailableStock": 1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": "WEATHER here Sun Wind Festival",
              "Id": "ApryllForever.CPPolyamorySweet_AtagartisPendantSunny",
              "ItemId": "ApryllForever.CPPolyamorySweet_AtagartisPendant",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "TradeItemId": null,
              "TradeItemAmount": 1,
              "Price": 20000,
              "ApplyDifficultyModifier": null,
              "AvailableStock": 1,
              "AvailableStockLimit": "Global",
              "AvoidRepeat": false,
              "IgnoreShopPriceModifiers": false,
              "PriceModifiers": null,
              "PriceModifierMode": "Stack",
              "AvailableStockModifiers": null,
              "AvailableStockModifierMode": "Stack",
              "Condition": null,
              "Id": "ApryllForever.CPPolyamorySweet_RoomieBGone",
              "ItemId": "ApryllForever.CPPolyamorySweet_RoomieBGone",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            }

          ],
          "CustomFields": {}
        }

      }
    },


    //Location


    {
      "Action": "EditData",
      "Target": "Data/Locations",
      "When": {
        "Lantana_Disable": false
      },
      "Entries": {
        "Custom_LantanaLagoon": {


          "DefaultArrivalTile": {
            "X": 15,
            "Y": 15
          },
          "ExcludeFromNpcPathfinding": false,

          "ArtifactSpots": [
            {
              "Chance": 0.2,
              "ApplyGenerousEnchantment": true,
              "OneDebrisPerDrop": true,
              "Precedence": -1000,
              "ContinueOnDrop": false,
              //"Condition": "",
              "Id": "(O)710",
              "ItemId": "(O)710", //Crab
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.25,
              "ApplyGenerousEnchantment": true,
              "OneDebrisPerDrop": true,
              "Precedence": -1000,
              "ContinueOnDrop": false,
              //"Condition": "",
              "Id": "(O)716",
              "ItemId": "(O)716", //Crayfish
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.05,
              "ApplyGenerousEnchantment": true,
              "OneDebrisPerDrop": true,
              "Precedence": -1000,
              "ContinueOnDrop": false,
              "Condition": null,
              "Id": "(O)797",
              "ItemId": "(O)797", //Pearl
              "RandomItemId": null,
              "MinStack": 1,
              "MaxStack": 3,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.2,
              "ApplyGenerousEnchantment": true,
              "OneDebrisPerDrop": true,
              "Precedence": 0,
              "ContinueOnDrop": false,
              "Condition": null,
              "Id": "(O)292",
              "ItemId": "(O)292", //Mahogany Seed
              "RandomItemId": null,
              "MinStack": 1,
              "MaxStack": 3,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.5,
              "ApplyGenerousEnchantment": true,
              "OneDebrisPerDrop": true,
              "Precedence": 0,
              "ContinueOnDrop": false,
              "Condition": null,
              "Id": "(O)393",
              "ItemId": "(O)393", //Coral
              "RandomItemId": null,
              "MinStack": 1,
              "MaxStack": 3,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 1.0,
              "ApplyGenerousEnchantment": true,
              "OneDebrisPerDrop": true,
              "Precedence": 0,
              "ContinueOnDrop": false,
              "Condition": null,
              "Id": "(O)749",
              "ItemId": "(O)749", // Omni Geode
              "RandomItemId": null,
              "MinStack": 1,
              "MaxStack": 3,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            }
          ],
          "FishAreas": {
            "Freshwater": {
              "DisplayName": null,
              "Position": {
                "X": 24,
                "Y": 0,
                "Width": 255,
                "Height": 100
              },
              "CrabPotFishTypes": [],
              "CrabPotJunkChance": 0.2
            }

          },
          "Fish": [

            {
              "Chance": 1.0,
              "Season": null,
              "FishAreaId": "Freshwater",
              "BobberPosition": null,
              "PlayerPosition": null,
              "MinFishingLevel": 0,
              "MinDistanceFromShore": 0,
              "MaxDistanceFromShore": -1,
              "ApplyDailyLuck": false,
              "CuriosityLureBuff": -1.0,
              "CatchLimit": -1,
              "IsBossFish": false,
              "SetFlagOnCatch": null,
              "RequireMagicBait": false,
              "Precedence": 0,
              "IgnoreFishDataRequirements": false,
              "CanBeInherited": true,
              "ChanceModifiers": null,
              "ChanceModifierMode": "Stack",
              "Condition": null,
              "Id": "(O)701",
              "ItemId": "(O)701", //Tilapia
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 1.0,
              "Season": null,
              "FishAreaId": "Freshwater",
              "BobberPosition": null,
              "PlayerPosition": null,
              "MinFishingLevel": 0,
              "MinDistanceFromShore": 0,
              "MaxDistanceFromShore": -1,
              "ApplyDailyLuck": false,
              "CuriosityLureBuff": -1.0,
              "CatchLimit": -1,
              "IsBossFish": false,
              "SetFlagOnCatch": null,
              "RequireMagicBait": false,
              "Precedence": 0,
              "IgnoreFishDataRequirements": false,
              "CanBeInherited": true,
              "ChanceModifiers": null,
              "ChanceModifierMode": "Stack",
              "Condition": null,
              "Id": "(O)145",
              "ItemId": "(O)145", //Sunfish
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 1.0,
              "Season": null,
              "FishAreaId": "Freshwater",
              "BobberPosition": null,
              "PlayerPosition": null,
              "MinFishingLevel": 0,
              "MinDistanceFromShore": 0,
              "MaxDistanceFromShore": -1,
              "ApplyDailyLuck": false,
              "CuriosityLureBuff": -1.0,
              "CatchLimit": -1,
              "IsBossFish": false,
              "SetFlagOnCatch": null,
              "RequireMagicBait": false,
              "Precedence": 0,
              "IgnoreFishDataRequirements": false,
              "CanBeInherited": true,
              "ChanceModifiers": null,
              "ChanceModifierMode": "Stack",
              "Condition": null,
              "Id": "(O)136",
              "ItemId": "(O)136", // Lg Bass
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 1.0,
              "Season": null,
              "FishAreaId": "Freshwater",
              "BobberPosition": null,
              "PlayerPosition": null,
              "MinFishingLevel": 0,
              "MinDistanceFromShore": 0,
              "MaxDistanceFromShore": -1,
              "ApplyDailyLuck": false,
              "CuriosityLureBuff": -1.0,
              "CatchLimit": -1,
              "IsBossFish": false,
              "SetFlagOnCatch": null,
              "RequireMagicBait": false,
              "Precedence": 0,
              "IgnoreFishDataRequirements": false,
              "CanBeInherited": true,
              "ChanceModifiers": null,
              "ChanceModifierMode": "Stack",
              "Condition": null,
              "Id": "(O)137",
              "ItemId": "(O)137", // Sm Bass
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 1.0,
              "Season": null,
              "FishAreaId": "Freshwater",
              "BobberPosition": null,
              "PlayerPosition": null,
              "MinFishingLevel": 0,
              "MinDistanceFromShore": 0,
              "MaxDistanceFromShore": -1,
              "ApplyDailyLuck": false,
              "CuriosityLureBuff": -1.0,
              "CatchLimit": -1,
              "IsBossFish": false,
              "SetFlagOnCatch": null,
              "RequireMagicBait": false,
              "Precedence": 0,
              "IgnoreFishDataRequirements": false,
              "CanBeInherited": true,
              "ChanceModifiers": null,
              "ChanceModifierMode": "Stack",
              "Condition": null,
              "Id": "(O)700", //BullHead
              "ItemId": "(O)700",
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            }
          ],
          "Forage": [
            {
              "Chance": 0.9,
              "Season": "spring",
              "Condition": null,
              "Id": "(O)259",
              "ItemId": "(O)259", //Fern
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.7,
              "Season": "spring",
              "Condition": null,
              "Id": "(O)372",
              "ItemId": "(O)372", //Clam
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.7,
              "Season": "summer",
              "Condition": null,
              "Id": "(O)372",
              "ItemId": "(O)372", //Clam
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.2,
              "Season": "summer",
              "Condition": null,
              "Id": "(O)394",
              "ItemId": "(O)394", //Rainbow Shell
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },

            {
              "Chance": 0.2,
              "Season": "winter",
              "Condition": null,
              "Id": "(O)394",
              "ItemId": "(O)394", //Rainbow Shell
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.9,
              "Season": "summer",
              "Condition": null,
              "Id": "(O)396",
              "ItemId": "(O)396", //SpiceBerry
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            },
            {
              "Chance": 0.9,
              "Season": "fall",
              "Condition": null,
              "Id": "(O)259",
              "ItemId": "(O)259", //Fern
              "RandomItemId": null,
              "MinStack": -1,
              "MaxStack": -1,
              "Quality": -1,
              "ObjectInternalName": null,
              "ObjectDisplayName": null,
              "ToolUpgradeLevel": -1,
              "IsRecipe": false,
              "StackModifiers": null,
              "StackModifierMode": "Stack",
              "QualityModifiers": null,
              "QualityModifierMode": "Stack"
            }
          ],
          "MinDailyWeeds": 0,
          "MaxDailyWeeds": 1,
          "FirstDayWeedMultiplier": 1,
          "MinDailyForageSpawn": 3,
          "MaxDailyForageSpawn": 7,
          "MaxSpawnedForageAtOnce": 7,
          "ChanceForClay": 0.25,
          "Music": [],
          "MusicDefault": null,
          "MusicContext": "Default",
          "MusicIgnoredInRain": false,
          "MusicIgnoredInSpring": false,
          "MusicIgnoredInSummer": false,
          "MusicIgnoredInFall": false,
          "MusicIgnoredInFallDebris": false,
          "MusicIgnoredInWinter": false,
          "MusicIsTownTheme": false,
          "CustomFields": {}
        }
      }
    },



    //Lantana


    {
      "Action": "EditData",
      "Target": "Data/Characters",
      "When": {
        "Lantana_Disable": false
      },
      "Entries": {
        "MermaidLantana": {
          "DisplayName": "兰塔娜",
          "BirthSeason": "Summer",
          "BirthDay": 2,
          "HomeRegion": "Other",
          "Gender": "Female",
          "Age": "Teen",
          "Manner": "Rude",
          "SocialAnxiety": "Outgoing",
          "Optimism": "Negative",

          "CanBeRomanced": false,
          "LoveInterest": "Oksana",
          "CanVisitIslandCondition": false,
          "IntroductionsQuest": false,
          "PerfectionScore": false,
          "WinterStarParticipant": false,

          "FriendsAndFamily": {
          },
          "Home": [
            {
              "Id": "Default",
              "Location": "Custom_LantanaLagoon",
              "Tile": {
                "X": 20,
                "Y": 7
              },
              "Direction": "Down"
            }
          ]
        }
      }
    },

    {
      "Action": "Load",
      "Target": "Data/Events/Custom_LantanaLagoon",
      "FromFile": "assets/Blank.json"
    },
      {
      "Action": "Load",
      "Target": "Data/Events/Custom_LantanaTemple",
      "FromFile": "assets/Blank.json"
    },

    {
      "LogName": "Lantana Gift Tastes",
      "Action": "EditData",
      "Priority": "Default",
      "Target": "Data/NPCGiftTastes",
      "When": {
        "Lantana_Disable": false
      },
      "Entries": {
        "MermaidLantana": "这太棒了！！！这个吻是回礼，亲爱的！！！ *MUA*/66 234 238 239 241 394 421 577 578 595 612 611 716/谢谢你，亲爱的！！！/-79 -80 -26 -12 -2 88/谁会喜欢这种东西？//这太糟了！！！你最好看看医生！！！/-81 -75 -27 -6 -5 -4 815 814 812 807 209 214 198/好吧...谢谢！/-20 -7/"
      }
    },
    {
      "LogName": "Lantana Sprite",
      "Action": "Load",
      "Priority": "Low",
      "Target": "Characters/MermaidLantana",
      "When": {
        "Lantana_Disable": false
      },
      "FromFile": "{{LantanaSprite}}"
    },

    {
      "LogName": "Lantana Portrait",
      "Action": "Load",
      "Priority": "Low",
      "Target": "Portraits/MermaidLantana",
      "When": {
        "Lantana_Disable": false
      },
      "FromFile": "assets/Lantana/portrait.png"
    },
    {
      "LogName": "Lantana Schedule",
      "Action": "Load",
      "Priority": "Low",
      "Target": "Characters/schedules/MermaidLantana",
      "When": {
        "Lantana_Disable": false
      },
      "FromFile": "assets/Lantana/schedule.json"
    },
    {
      "LogName": "Lantana Dialogue",
      "Action": "Load",
      "Priority": "Low",
      "Target": "Characters/Dialogue/MermaidLantana",
      "When": {
        "Lantana_Disable": false
      },
      "FromFile": "assets/Lantana/Dialogue.json"
    },

    {
      "LogName": "Lantana Schedule Dialogue",
      "Action": "Load",
      "Priority": "Low",
      "Target": "Strings/schedules/MermaidLantana",
      "When": {
        "Lantana_Disable": false
      },
      "FromFile": "assets/Lantana/scheduleDialogue.json"
    },

    {
      "Action": "EditData",
      "Target": "Data/Weddings",
      "When": {
        "Lantana_Disable": false
      },
      "TargetField": [ "Attendees" ],
      "Entries": {
        "MermaidLantana": {
          "Id": "MermaidLantana",
          "Condition": null,
          "Setup": "{{LantanaWeddingLocation}}",
          "Celebration": null,
          "IgnoreUnlockConditions": false
        }
      }
    },




    {
      "Action": "EditData",
      "When": {
        "Lantana_Disable": false
      },
      "Target": "Data/Events/Farm",
      "Entries": {
        "PolyamorySweetLantanaFarm/n ccBoilerRoom": "jaunty/-500 -500/farmer 64 16 2 MermaidLantana 64 18 0/viewport 64 17 true/pause 800/speak MermaidLantana \"嘿你好，@！我是人鱼兰塔娜！我喜欢你的农场！$1#$b#你一定在想，为什么会有人鱼出现在你家门口！\"/pause 300/emote farmer 8/pause 900/speak MermaidLantana \"我只是想让你知道，现在镇上的矿车修好了，你可以乘坐矿车来我在礁湖的小店坐坐。\"/pause 600/jump farmer/pause 1100/speak MermaidLantana \"如果你想和某人约会，请送TA一束美人鱼花束吧！只要你愿意，你可以和镇上的每个单身镇民约会！$1#$b#如果你想要和某人结婚，请必须先和TA约会，并且达到十心好感！然后，你可以送TA一枚阿塔加蒂斯吊坠作为定情信物！\"/pause 400/emote MermaidLantana 20/pause 500/speak MermaidLantana \"仔细听，这部分很重要：你同时只能和一个人订婚！$1#$b#除此之外，如果你有一个不愿搬走的钉子户室友，你可以送TA一根‘分居蜡烛’，保证24小时内起效！#$b#不过，这根蜡烛对于你已经结婚的任何伴侣都不起效。TA会觉得是你糊涂了！你必须先和TA离婚才行。#$b#和你聊天很开心！祝你今天过得愉快，亲爱的！^和你聊天真开心！祝你今天过得开心，小可爱！\"/pause 300/emote farmer 20/emote MermaidLantana 20/pause 1200/end"
      }
    },

    {
      "Action": "EditData",
      "When": {
      "Relationship:Abigail": "Married"
          },
      "Target": "Characters/Dialogue/Abigail",
      "Entries": {

        "summer_Wed2": "我喜欢和你一起，住在旧农舍里。#$b#地板的嘎吱声，椽子间飘起的尘烟...这里充满了想象的空间！$1",
        "summer_Tue4": "你这人真有趣，@。能和你在一起真是太好了！$h",
        "summer_Sat6": "夏天或许很热，但是...$h#$b#至少这意味着秋天即将到来！$1",
        "summer_Mon8": "有你在，鹈鹕镇变得更美好了！$h#$b#这是给你的一点小礼物，代表着我的爱！[226]",
        "summer_Thu10": "哇，你今天看起来真不错。#$b#很高兴我让你养成了洗脸和保湿的好习惯！$l^和你结婚有个好处，那就是我可以偷用你的化妆品！$1",
        "summer_Fri10": "我很高兴能让你在老农场里不再孤单！$1",

        "Wed2": "啊，你好啊，@。你是在休息吗？#$b#我也是。只是在想，我拿到的网上大学学位能派上什么用场...#$b#如果钱实在太紧张，也许我可以找一份在家的工作？",
        "Tue4": "很高兴遇到你出来走走！$h#$e#今天过得怎么样？希望你过得愉快！$1",
        "Sat6": "你知道吗，自从你第一次在山洞里从蝙蝠群中救了我之后，我在那里经历了一些相当疯狂的冒险！$l#$b#别担心，没什么太危险的！我非常小心，因为我爱你！",
        "Mon8": "来！让我看看你的手相！$h#$b#我看到你未来会有一个假期，而且你可爱的紫发妻子会陪在你身边！$1",
        "Thu10": "嗨。$l#$e#你有没有觉得，最近一切都没有真实感？$l#$b#但这样并没有什么不好。$h",
        "Fri6": "鸟儿们今天高兴地唧唧喳喳地叫着。看到它们在一起，让我想到有你陪伴是多么幸福！#$b#还有给你的一点小礼物，代表着我的爱！[226]",


        "winter_Fri10": "@，有件事我想和你说...#$b#你真可爱，能和你结婚我真是太幸运了！#$b#就这件事！$1^我很高兴我终于接受了真实的自我，一个拉拉，并且和世界上最漂亮的女孩结婚！$6",
        "winter_Sat6": "我最近一直在做白日梦...#$b#噢！是关于你的，当然了！$h^是关于你的，我的爱人，还有你甜蜜的吻！$1",
        "winter_Thu10": "这一年又要过去了...#$b#很高兴明年能和你一起度过！$h",
        "winter_Wed6": "每年到这个时候，就不需要操心庄稼了，真是太好了。#$e#而那也意味着，你就有更多时间来陪我啦！$h#$b#还有给你的一点小礼物，代表着我的爱！[226]",

        "fall_Wed2": "我们今年种南瓜了吗？帮我留一个吧！$h#$b#还有给你的一点小礼物，代表着我的爱！[226]",
        "fall_Tue4": "和你一起生活真好！#$b#别误会，我当然爱我的父母，但他们似乎从未完全意识到我已经是个大人了，直到我和你结婚！$2",
        "fall_Sat6": "今天你的头发好酷啊...你做了什么改变吗？^今天你的头发好漂亮啊！你是如此美丽，我的爱人！$1",
        "fall_Mon8": "我从未想过自己会成为一位农夫的妻子...#$b#但你知道吗？这太不可思议了！*咯咯笑*$l^但你知道吗？能成为你的妻子真是太棒了！你成为我的妻子，对我来说也是太棒了！$6",
        "fall_Thu10": "我昨天晚上做了一个很有趣的梦，还梦到了你。#$b#那真是个美梦。$h",
        "fall_Fri10": "我希望这个季节能再长一些。$8#$b#等风一起，感觉自己又回到了童年呢。$1",

        "spring_23": "好吧，明天就是花舞节了。我还是讨厌这条傻里傻气的白裙子...$s",
        "summer_27": "我们绝对不能错过明天的庆典。那是星露谷最美的盛景之一。#$b#和你一起观看更是妙不可言！",
      }
    },

    {
      "Action": "EditData",
      "When": {
      "Relationship:Leah": "Married"
          },
      "Target": "Characters/Dialogue/Leah",
      "Entries": {
        "spring_Tue4": "目睹世界从冬日的沉睡中苏醒，焕发生机，是灵魂最令人振奋的滋补品！",
        "spring_Wed8": "春风是最清新的气息！#$b#也许有一天我能调制出这样的香水！$l^亲爱的，也许我们可以调制并喷上这样的香水！$l",
        "spring_Thu6": "采集是我的特长。来，尝尝这新鲜的沙拉！[196 609 610]$h",
        "spring_Fri6": "有时候，我会产生一种奇怪的感觉。我的头脑变得一片空白...我感到完全平静，甚至有些幸福。#$b#嘿，听着！这与喝酒无关！$h",
        "spring_Sat8": "做农活太有趣了。我一直都想尝试一下。现在，我的梦想成真了！$l",
        "spring_Sun8": "如果你有找到样貌有趣的浮木的话，我可能会用的着。#$e#从你那里得到这样的礼物真的很特别。",

        "summer_Mon2": "我今天早上找到了一些野生的果子！#$b#抱歉，我已经吃光了！$1",
        "summer_Thu4": "农场的声音与气息真是美妙，不是吗？#$b#能和你一起住在这里，我真是太幸运了！^有你做我的妻子，能和你一起在农场生活，我真是太幸福了！$1",
        "summer_Tue4": "生活中简单的东西最棒了：夏天里一阵柔和的清风，宏伟的云，或者是一个装满了星露谷红酒的酒杯。$h^生活中简单的东西最棒了：夏天里一阵柔和的清风，宏伟的云，或者是一个装满了星露谷红酒的酒杯，以及作为一个拉拉爱上你！$h",
        "summer_Fri4": "我喜欢走进森林……稍作停留……然后静静地欣赏大自然的美景。#$b#我们很容易忘记这个世界实际上是多么的不可思议...",
        "summer_Sat4": "水流能让我们的房子夏天的时候能够稍微凉快一些。#$b#我不是很能忍受炎热，所以很感激这份恩赐！",
        "summer_Sun4": "在这个炎热又慵懒的日子里我该做什么呢？*唉*#$b#有什么建议吗？#$b#还有给你的一点小礼物，代表着我的爱！[205]",
        "fall_Mon2": "我喜欢山谷里这么多蘑菇！#$b#而且我喜欢你给我的那些！$1",
        "fall_Tue4": "我喜欢秋天的色彩搭配...温暖的红色、黄色和橙色..",
        "fall_Wed2": "住在河边真的太棒了。#$b#我每天都伴着轻柔的水流声，在你身旁入睡...$6",
        "fall_Thu6": "等哪天有时间，我要给你做一份炒野生菌。$h",
        "fall_Fri6": "我喜欢为你做饭。$h#$b#今天早些时候我特别为你做了这个！[606]",
        "fall_Sat4": "装饰让四季的感觉更鲜明了。我觉得这个传统挺有意义的。",
        "fall_Sun4": "我喜欢这个地方的一点是，这里有美丽的瀑布。#$b#还有你！^还有我亲爱的妻子！$1",
        "winter_Mon4": "我很喜欢一切都被新雪覆盖住的样子。",
        "winter_Tue4": "这里有时候会变得很冷...$s#$b#不过，我只是很喜欢依偎在你身边！$h",
        "winter_Wed4": "我今天早上看到雪兔了！它们可是非常稀有的呢，不是吗？",
        "winter_Thu6": "*唉*...冬天里很难找到新鲜的食物。#$e#我猜你的农场里应该过得挺好的吧，是不是装满了食物？",
        "winter_Fri6": "你跑步时会不会觉得冷？#$b#想进来的话，可以躲进我的被子里喝杯苹果酒。$h",
        "winter_Sat2": "你好，@。很高兴遇到你出来走走！",
        "winter_Sun4": "你应该偶尔休息一整天。#$b#这不仅对心灵有好处，而且从长远来看，你实际上会更有生产力！"
      }
    },





    {
      "Action": "EditData",
      "When": {
      "Relationship:Haley": "Married"
          },
      "Target": "Characters/Dialogue/Haley",
      "Entries": {

    
  "spring_Wed8": "以前，这个小镇，只有两点是我喜欢的。#$b#一个是海滩。另一个是你！*咯咯笑*$h#$b#但现在，我真的爱上了这里的自然风光！$1",
  "spring_Tue2": "今早海滩会有美丽的贝壳被冲上来吗？#$e#虽然现在不是一年中捡贝壳的最好时节。",
  "spring_Mon6": "我以前老是抱怨这个镇太小了，但是我越来越喜欢它了。#$b#假如太大的话，就不会有归属感了。#$b#我们可以在这里养育孩子，让他们深深扎根于自然和社区！",
  "spring_Thu6": "我昨晚做了晚餐。感觉还不错！#$e#但是我把厨房搞得一团糟...",
  "spring_Sun6": "我喜欢俯瞰海滩！这个地方真美！",
  "spring_Sat8": "这些花终于要开了。#$b#那些粉色的好香。$h",
  "spring_Fri10": "@，我们有空去野营吧？#$b#我去过的最怪异的地方就是祖祖城了。",

  "summer_Wed8": "今天好热！#$b#能给我一个冰淇淋吗，亲爱的？$8",
  "summer_Thu6": "有时候你会长时间待在阳光下...但我不想你被晒伤，亲爱的！#$b#我只想要你健康!$1",
  "summer_Mon6": "我最近读了一些书...#$b#我了解到保护我们当地的生态环境是多么重要！",
  "summer_Sun6": "夏天的日落和夜晚的凉爽，无与伦比！",
  "summer_Sat6": "看起来是个游泳的好日子。",
  "summer_Tue8": "我现在喜欢住在乡下了。#$b#我看待事情的方式和以前大不一样了！我也很高兴能和我姐姐住在同一个地方！$h",
  "summer_Fri8": "啊，你好啊，@！#$e#你喜欢我的打扮吗？$l",

  "fall_Fri4": "你好像忙坏了啊。#$b#为什么不休息一下呢？",
  "fall_Wed4": "要是无聊了，来和我呆一会吧！$l",
  "fall_Thu6": "自从认识你以来，我确实成长了很多。#$b#你竟然喜欢我，我太幸运了！^你和我一样是拉拉，我太幸运了！^你是我的妻子，我太爱你了！$l",
   "fall_Sun6": "我喜欢在森林里拍照！#$b#多亏了你，因为是你帮助我突破了自我！$h#$b#我喜欢和你在一起！$l",
  "fall_Tue8": "我喜欢树叶的味道！#$b#我对秋天有了全新的观感！$h",
  "fall_Mon10": "@，今天很高兴见到你。$h#$b#你比向日葵还让我开心！$l",
  "fall_Sat6": "我刚刚在落叶中跳舞了！#$b#太有趣了！$1",

  "winter_Tue4": "嗨，，@。#$b#农场上有什么好玩的事吗？#$b#比如，昨晚我看到一只大脚怪在闻花！#$b#太可爱了！$9",
  "winter_Wed6": "我喜欢住在农场！#$b#我也很喜欢漂亮的衣服和潮流。仅仅因为我们是农民，并不意味着我们不能变得时尚！$h",
  "winter_Thu6": "冬日的日落真是美不胜收。$h#$b#一年中如此浪漫的时刻...#$b#...什么？$l",
  "winter_Mon8": "星露谷要是能暖和一些就好了。#$b#我们可以搬到别的地方，但是...我喜欢这个农场！",
  "winter_Fri8": "你觉得我们将来会有...",
  "winter_Sat8": "冬天你就不用太操劳了，真是挺好呢。#$b#能多和你相处真是太好了！",
  "winter_Sun10": "@，你今天精神不错啊，我喜欢。^@，你今天很漂亮嘛，我喜欢！$l#$e#...$h"


  }
},
    // {
    //"Action": "EditData",
    //"Target": "Strings/StringsFromCSFiles",
    //"Entries": {
    // "FlowerDanceFemaleAccept": "你想做我的舞伴吗？#$b#当然可以！我非常想和你跳舞！ <$h",
    // "FlowerDanceMaleAccept": "你想做我的舞伴吗？#$b#当然可以！我非常想和你跳舞！ <$h"
    //}
    //}

      {
      "Action": "EditData",
      "Target": "Strings/UI",
      "Entries": {
        "Chat_DeclineProposal": "{1} is disinclined to accept the proposal of marriage profferred by one {0}."

      }
    },
    
{  //FarmhouseUpgrade
            "Action": "EditMap",
              "LogName": "Farmhouse Map Expander",
            "Target": "Maps/FarmHouse",
            "FromFile": "Assets/FarmHousePatch.tmx",
"When": {
   "FarmhouseUpgrade: currentPlayer": "{{Range: 1, 3}}"
},


            "FromArea": { "X": 36, "Y": 0, "Width": 0, "Height": 0 },
            "ToArea": { "X": 194, "Y": 120, "Width": 0, "Height": 0 }
        },
 {
            "Action": "EditMap",
               "LogName": "Farmhouse Map Expander",
            "Target": "Maps/FarmHouse1_marriage",
            "FromFile": "Assets/FarmHousePatch.tmx",
            "FromArea": { "X": 36, "Y": 0, "Width": 0, "Height": 0 },

            "When": {
   "FarmhouseUpgrade: currentPlayer": "{{Range: 1, 3}}"
},
            "ToArea": { "X": 194, "Y": 120, "Width": 0, "Height": 0 }
        },
{
            "Action": "EditMap",
               "LogName": "Farmhouse Map Expander",
            "Target": "Maps/FarmHouse2_marriage",
            "FromFile": "Assets/FarmHousePatch.tmx",
            "FromArea": { "X": 36, "Y": 0, "Width": 0, "Height": 0 },

            "When": {
   "FarmhouseUpgrade: currentPlayer": "{{Range: 1, 3}}"
},
            "ToArea": { "X": 194, "Y": 120, "Width": 0, "Height": 0 }
        },

  {
                "Action": "EditData",
                "Target": "Mods/CJBok.CheatsMenu/WarpSections",
                "Entries": {
                    "ApryllForever.CPPolyamorySweet_MermaidSection": {
                        "Id": "ApryllForever.CPPolyamorySweet_MermaidLocations",
                        "DisplayName": "{{i18n:ApryllForever.MermaidLocations}}"
                    },
                   
                }
            },

 {
                "Action": "EditData",
                "Target": "Mods/CJBok.CheatsMenu/Warps",
                "Entries": {
                    "ApryllForever.PolyamorySweet_LantanaLagoon": {
                        "Id": "ApryllForever.CPPolyamorySweet_Lantana Lagoon",
                        "DisplayName": "{{i18n:DisplayName.LantanaLagoon}}",
                        "Location": "Custom_LantanaLagoon",
                        "Tile": "15, 15",
                        "SectionId": "ApryllForever.CPPolyamorySweet_MermaidLocations"
                    },
                  }
                }


  ]
}//Apryll